Collecting google-adk
  Downloading google_adk-1.5.0-py3-none-any.whl.metadata (10 kB)
Requirement already satisfied: PyYAML>=6.0.2 in c:\users\<USER>\appdata\local\programs\python\python39\lib\site-packages (from google-adk) (6.0.2)
Collecting authlib>=1.5.1 (from google-adk)
  Downloading authlib-1.6.0-py2.py3-none-any.whl.metadata (4.1 kB)
Requirement already satisfied: click>=8.1.8 in c:\users\<USER>\appdata\local\programs\python\python39\lib\site-packages (from google-adk) (8.1.8)
Requirement already satisfied: fastapi>=0.115.0 in c:\users\<USER>\appdata\local\programs\python\python39\lib\site-packages (from google-adk) (0.115.14)
Collecting google-api-python-client>=2.157.0 (from google-adk)
  Downloading google_api_python_client-2.174.0-py3-none-any.whl.metadata (7.0 kB)
Collecting google-cloud-aiplatform>=1.95.1 (from google-cloud-aiplatform[agent-engines]>=1.95.1->google-adk)
  Downloading google_cloud_aiplatform-1.100.0-py2.py3-none-any.whl.metadata (38 kB)
Collecting google-cloud-secret-manager>=2.22.0 (from google-adk)
  Downloading google_cloud_secret_manager-2.24.0-py3-none-any.whl.metadata (9.7 kB)
Collecting google-cloud-speech>=2.30.0 (from google-adk)
  Downloading google_cloud_speech-2.33.0-py3-none-any.whl.metadata (9.6 kB)
Collecting google-cloud-storage<3.0.0,>=2.18.0 (from google-adk)
  Downloading google_cloud_storage-2.19.0-py2.py3-none-any.whl.metadata (9.1 kB)
Collecting google-genai>=1.21.1 (from google-adk)
  Downloading google_genai-1.23.0-py3-none-any.whl.metadata (38 kB)
Collecting graphviz>=0.20.2 (from google-adk)
  Downloading graphviz-0.21-py3-none-any.whl.metadata (12 kB)
Collecting opentelemetry-api>=1.31.0 (from google-adk)
  Downloading opentelemetry_api-1.34.1-py3-none-any.whl.metadata (1.5 kB)
Collecting opentelemetry-exporter-gcp-trace>=1.9.0 (from google-adk)
  Downloading opentelemetry_exporter_gcp_trace-1.9.0-py3-none-any.whl.metadata (3.3 kB)
Collecting opentelemetry-sdk>=1.31.0 (from google-adk)
  Downloading opentelemetry_sdk-1.34.1-py3-none-any.whl.metadata (1.6 kB)
Requirement already satisfied: pydantic<3.0.0,>=2.0 in c:\users\<USER>\appdata\local\programs\python\python39\lib\site-packages (from google-adk) (2.10.6)
Requirement already satisfied: python-dateutil>=2.9.0.post0 in c:\users\<USER>\appdata\local\programs\python\python39\lib\site-packages (from google-adk) (2.9.0.post0)
Requirement already satisfied: python-dotenv>=1.0.0 in c:\users\<USER>\appdata\local\programs\python\python39\lib\site-packages (from google-adk) (1.0.1)
Collecting requests>=2.32.4 (from google-adk)
  Downloading requests-2.32.4-py3-none-any.whl.metadata (4.9 kB)
Requirement already satisfied: sqlalchemy>=2.0 in c:\users\<USER>\appdata\local\programs\python\python39\lib\site-packages (from google-adk) (2.0.38)
Requirement already satisfied: starlette>=0.46.2 in c:\users\<USER>\appdata\local\programs\python\python39\lib\site-packages (from google-adk) (0.46.2)
Requirement already satisfied: typing-extensions<5,>=4.5 in c:\users\<USER>\appdata\local\programs\python\python39\lib\site-packages (from google-adk) (4.12.2)
Collecting tzlocal>=5.3 (from google-adk)
  Using cached tzlocal-5.3.1-py3-none-any.whl.metadata (7.6 kB)
Requirement already satisfied: uvicorn>=0.34.0 in c:\users\<USER>\appdata\local\programs\python\python39\lib\site-packages (from google-adk) (0.34.3)
Collecting websockets>=15.0.1 (from google-adk)
  Downloading websockets-15.0.1-cp39-cp39-win_amd64.whl.metadata (7.0 kB)
Collecting cryptography (from authlib>=1.5.1->google-adk)
  Downloading cryptography-45.0.4-cp37-abi3-win_amd64.whl.metadata (5.7 kB)
Requirement already satisfied: colorama in c:\users\<USER>\appdata\local\programs\python\python39\lib\site-packages (from click>=8.1.8->google-adk) (0.4.6)
Collecting httplib2<1.0.0,>=0.19.0 (from google-api-python-client>=2.157.0->google-adk)
  Downloading httplib2-0.22.0-py3-none-any.whl.metadata (2.6 kB)
Requirement already satisfied: google-auth!=2.24.0,!=2.25.0,<3.0.0,>=1.32.0 in c:\users\<USER>\appdata\local\programs\python\python39\lib\site-packages (from google-api-python-client>=2.157.0->google-adk) (2.40.3)
Collecting google-auth-httplib2<1.0.0,>=0.2.0 (from google-api-python-client>=2.157.0->google-adk)
  Downloading google_auth_httplib2-0.2.0-py2.py3-none-any.whl.metadata (2.2 kB)
Requirement already satisfied: google-api-core!=2.0.*,!=2.1.*,!=2.2.*,!=2.3.0,<3.0.0,>=1.31.5 in c:\users\<USER>\appdata\local\programs\python\python39\lib\site-packages (from google-api-python-client>=2.157.0->google-adk) (2.25.1)
Collecting uritemplate<5,>=3.0.1 (from google-api-python-client>=2.157.0->google-adk)
  Downloading uritemplate-4.2.0-py3-none-any.whl.metadata (2.6 kB)
Requirement already satisfied: proto-plus<2.0.0,>=1.22.3 in c:\users\<USER>\appdata\local\programs\python\python39\lib\site-packages (from google-cloud-aiplatform>=1.95.1->google-cloud-aiplatform[agent-engines]>=1.95.1->google-adk) (1.26.1)
Requirement already satisfied: protobuf!=4.21.0,!=4.21.1,!=4.21.2,!=4.21.3,!=4.21.4,!=4.21.5,<7.0.0,>=3.20.2 in c:\users\<USER>\appdata\local\programs\python\python39\lib\site-packages (from google-cloud-aiplatform>=1.95.1->google-cloud-aiplatform[agent-engines]>=1.95.1->google-adk) (6.31.1)
Requirement already satisfied: packaging>=14.3 in c:\users\<USER>\appdata\local\programs\python\python39\lib\site-packages (from google-cloud-aiplatform>=1.95.1->google-cloud-aiplatform[agent-engines]>=1.95.1->google-adk) (23.2)
Collecting google-cloud-bigquery!=3.20.0,<4.0.0,>=1.15.0 (from google-cloud-aiplatform>=1.95.1->google-cloud-aiplatform[agent-engines]>=1.95.1->google-adk)
  Downloading google_cloud_bigquery-3.34.0-py3-none-any.whl.metadata (8.0 kB)
Collecting google-cloud-resource-manager<3.0.0,>=1.3.3 (from google-cloud-aiplatform>=1.95.1->google-cloud-aiplatform[agent-engines]>=1.95.1->google-adk)
  Downloading google_cloud_resource_manager-1.14.2-py3-none-any.whl.metadata (9.6 kB)
Collecting shapely<3.0.0 (from google-cloud-aiplatform>=1.95.1->google-cloud-aiplatform[agent-engines]>=1.95.1->google-adk)
  Using cached shapely-2.0.7-cp39-cp39-win_amd64.whl.metadata (7.1 kB)
Collecting docstring_parser<1 (from google-cloud-aiplatform>=1.95.1->google-cloud-aiplatform[agent-engines]>=1.95.1->google-adk)
  Using cached docstring_parser-0.16-py3-none-any.whl.metadata (3.0 kB)
Collecting packaging>=14.3 (from google-cloud-aiplatform>=1.95.1->google-cloud-aiplatform[agent-engines]>=1.95.1->google-adk)
  Using cached packaging-25.0-py3-none-any.whl.metadata (3.3 kB)
Collecting cloudpickle<4.0,>=3.0 (from google-cloud-aiplatform[agent-engines]>=1.95.1->google-adk)
  Downloading cloudpickle-3.1.1-py3-none-any.whl.metadata (7.1 kB)
Collecting google-cloud-trace<2 (from google-cloud-aiplatform[agent-engines]>=1.95.1->google-adk)
  Downloading google_cloud_trace-1.16.2-py3-none-any.whl.metadata (9.7 kB)
Collecting google-cloud-logging<4 (from google-cloud-aiplatform[agent-engines]>=1.95.1->google-adk)
  Downloading google_cloud_logging-3.12.1-py2.py3-none-any.whl.metadata (5.0 kB)
Collecting pydantic<3.0.0,>=2.0 (from google-adk)
  Downloading pydantic-2.11.7-py3-none-any.whl.metadata (67 kB)
Collecting grpc-google-iam-v1<1.0.0,>=0.14.0 (from google-cloud-secret-manager>=2.22.0->google-adk)
  Downloading grpc_google_iam_v1-0.14.2-py3-none-any.whl.metadata (9.1 kB)
Collecting google-cloud-core<3.0dev,>=2.3.0 (from google-cloud-storage<3.0.0,>=2.18.0->google-adk)
  Downloading google_cloud_core-2.4.3-py2.py3-none-any.whl.metadata (2.7 kB)
Collecting google-resumable-media>=2.7.2 (from google-cloud-storage<3.0.0,>=2.18.0->google-adk)
  Downloading google_resumable_media-2.7.2-py2.py3-none-any.whl.metadata (2.2 kB)
Collecting google-crc32c<2.0dev,>=1.0 (from google-cloud-storage<3.0.0,>=2.18.0->google-adk)
  Downloading google_crc32c-1.7.1-cp39-cp39-win_amd64.whl.metadata (2.4 kB)
Requirement already satisfied: anyio<5.0.0,>=4.8.0 in c:\users\<USER>\appdata\local\programs\python\python39\lib\site-packages (from google-genai>=1.21.1->google-adk) (4.8.0)
Requirement already satisfied: httpx<1.0.0,>=0.28.1 in c:\users\<USER>\appdata\local\programs\python\python39\lib\site-packages (from google-genai>=1.21.1->google-adk) (0.28.1)
Requirement already satisfied: tenacity<9.0.0,>=8.2.3 in c:\users\<USER>\appdata\local\programs\python\python39\lib\site-packages (from google-genai>=1.21.1->google-adk) (8.5.0)
Requirement already satisfied: importlib-metadata<8.8.0,>=6.0 in c:\users\<USER>\appdata\local\programs\python\python39\lib\site-packages (from opentelemetry-api>=1.31.0->google-adk) (8.6.1)
Collecting opentelemetry-resourcedetector-gcp==1.*,>=1.5.0dev0 (from opentelemetry-exporter-gcp-trace>=1.9.0->google-adk)
  Downloading opentelemetry_resourcedetector_gcp-1.9.0a0-py3-none-any.whl.metadata (2.4 kB)
Collecting opentelemetry-semantic-conventions==0.55b1 (from opentelemetry-sdk>=1.31.0->google-adk)
  Downloading opentelemetry_semantic_conventions-0.55b1-py3-none-any.whl.metadata (2.5 kB)
Requirement already satisfied: annotated-types>=0.6.0 in c:\users\<USER>\appdata\local\programs\python\python39\lib\site-packages (from pydantic<3.0.0,>=2.0->google-adk) (0.7.0)
Collecting pydantic-core==2.33.2 (from pydantic<3.0.0,>=2.0->google-adk)
  Downloading pydantic_core-2.33.2-cp39-cp39-win_amd64.whl.metadata (6.9 kB)
Collecting typing-inspection>=0.4.0 (from pydantic<3.0.0,>=2.0->google-adk)
  Downloading typing_inspection-0.4.1-py3-none-any.whl.metadata (2.6 kB)
Requirement already satisfied: six>=1.5 in c:\users\<USER>\appdata\local\programs\python\python39\lib\site-packages (from python-dateutil>=2.9.0.post0->google-adk) (1.17.0)
Requirement already satisfied: charset_normalizer<4,>=2 in c:\users\<USER>\appdata\local\programs\python\python39\lib\site-packages (from requests>=2.32.4->google-adk) (3.4.1)
Requirement already satisfied: idna<4,>=2.5 in c:\users\<USER>\appdata\local\programs\python\python39\lib\site-packages (from requests>=2.32.4->google-adk) (3.10)
Requirement already satisfied: urllib3<3,>=1.21.1 in c:\users\<USER>\appdata\local\programs\python\python39\lib\site-packages (from requests>=2.32.4->google-adk) (2.3.0)
Requirement already satisfied: certifi>=2017.4.17 in c:\users\<USER>\appdata\local\programs\python\python39\lib\site-packages (from requests>=2.32.4->google-adk) (2025.1.31)
Requirement already satisfied: greenlet!=0.4.17 in c:\users\<USER>\appdata\local\programs\python\python39\lib\site-packages (from sqlalchemy>=2.0->google-adk) (3.1.1)
Requirement already satisfied: tzdata in c:\users\<USER>\appdata\local\programs\python\python39\lib\site-packages (from tzlocal>=5.3->google-adk) (2025.2)
Requirement already satisfied: h11>=0.8 in c:\users\<USER>\appdata\local\programs\python\python39\lib\site-packages (from uvicorn>=0.34.0->google-adk) (0.14.0)
Requirement already satisfied: exceptiongroup>=1.0.2 in c:\users\<USER>\appdata\local\programs\python\python39\lib\site-packages (from anyio<5.0.0,>=4.8.0->google-genai>=1.21.1->google-adk) (1.2.2)
Requirement already satisfied: sniffio>=1.1 in c:\users\<USER>\appdata\local\programs\python\python39\lib\site-packages (from anyio<5.0.0,>=4.8.0->google-genai>=1.21.1->google-adk) (1.3.1)
Requirement already satisfied: googleapis-common-protos<2.0.0,>=1.56.2 in c:\users\<USER>\appdata\local\programs\python\python39\lib\site-packages (from google-api-core!=2.0.*,!=2.1.*,!=2.2.*,!=2.3.0,<3.0.0,>=1.31.5->google-api-python-client>=2.157.0->google-adk) (1.70.0)
Requirement already satisfied: grpcio<2.0.0,>=1.33.2 in c:\users\<USER>\appdata\local\programs\python\python39\lib\site-packages (from google-api-core[grpc]!=2.0.*,!=2.1.*,!=2.2.*,!=2.3.*,!=2.4.*,!=2.5.*,!=2.6.*,!=2.7.*,<3.0.0,>=1.34.1->google-cloud-aiplatform>=1.95.1->google-cloud-aiplatform[agent-engines]>=1.95.1->google-adk) (1.73.1)
Requirement already satisfied: grpcio-status<2.0.0,>=1.33.2 in c:\users\<USER>\appdata\local\programs\python\python39\lib\site-packages (from google-api-core[grpc]!=2.0.*,!=2.1.*,!=2.2.*,!=2.3.*,!=2.4.*,!=2.5.*,!=2.6.*,!=2.7.*,<3.0.0,>=1.34.1->google-cloud-aiplatform>=1.95.1->google-cloud-aiplatform[agent-engines]>=1.95.1->google-adk) (1.73.1)
Requirement already satisfied: cachetools<6.0,>=2.0.0 in c:\users\<USER>\appdata\local\programs\python\python39\lib\site-packages (from google-auth!=2.24.0,!=2.25.0,<3.0.0,>=1.32.0->google-api-python-client>=2.157.0->google-adk) (5.5.2)
Requirement already satisfied: pyasn1-modules>=0.2.1 in c:\users\<USER>\appdata\local\programs\python\python39\lib\site-packages (from google-auth!=2.24.0,!=2.25.0,<3.0.0,>=1.32.0->google-api-python-client>=2.157.0->google-adk) (0.4.2)
Requirement already satisfied: rsa<5,>=3.1.4 in c:\users\<USER>\appdata\local\programs\python\python39\lib\site-packages (from google-auth!=2.24.0,!=2.25.0,<3.0.0,>=1.32.0->google-api-python-client>=2.157.0->google-adk) (4.9.1)
Collecting google-cloud-appengine-logging<2.0.0,>=0.1.3 (from google-cloud-logging<4->google-cloud-aiplatform[agent-engines]>=1.95.1->google-adk)
  Downloading google_cloud_appengine_logging-1.6.2-py3-none-any.whl.metadata (9.9 kB)
Collecting google-cloud-audit-log<1.0.0,>=0.3.1 (from google-cloud-logging<4->google-cloud-aiplatform[agent-engines]>=1.95.1->google-adk)
  Downloading google_cloud_audit_log-0.3.2-py3-none-any.whl.metadata (9.0 kB)
Collecting pyparsing!=3.0.0,!=3.0.1,!=3.0.2,!=3.0.3,<4,>=2.4.2 (from httplib2<1.0.0,>=0.19.0->google-api-python-client>=2.157.0->google-adk)
  Using cached pyparsing-3.2.3-py3-none-any.whl.metadata (5.0 kB)
Requirement already satisfied: httpcore==1.* in c:\users\<USER>\appdata\local\programs\python\python39\lib\site-packages (from httpx<1.0.0,>=0.28.1->google-genai>=1.21.1->google-adk) (1.0.7)
Requirement already satisfied: zipp>=3.20 in c:\users\<USER>\appdata\local\programs\python\python39\lib\site-packages (from importlib-metadata<8.8.0,>=6.0->opentelemetry-api>=1.31.0->google-adk) (3.21.0)
Requirement already satisfied: numpy<3,>=1.14 in c:\users\<USER>\appdata\local\programs\python\python39\lib\site-packages (from shapely<3.0.0->google-cloud-aiplatform>=1.95.1->google-cloud-aiplatform[agent-engines]>=1.95.1->google-adk) (1.26.4)
Collecting cffi>=1.14 (from cryptography->authlib>=1.5.1->google-adk)
  Using cached cffi-1.17.1-cp39-cp39-win_amd64.whl.metadata (1.6 kB)
Collecting pycparser (from cffi>=1.14->cryptography->authlib>=1.5.1->google-adk)
  Using cached pycparser-2.22-py3-none-any.whl.metadata (943 bytes)
Requirement already satisfied: pyasn1<0.7.0,>=0.6.1 in c:\users\<USER>\appdata\local\programs\python\python39\lib\site-packages (from pyasn1-modules>=0.2.1->google-auth!=2.24.0,!=2.25.0,<3.0.0,>=1.32.0->google-api-python-client>=2.157.0->google-adk) (0.6.1)
Downloading google_adk-1.5.0-py3-none-any.whl (1.3 MB)
   ---------------------------------------- 1.3/1.3 MB 17.0 MB/s eta 0:00:00
Downloading authlib-1.6.0-py2.py3-none-any.whl (239 kB)
Downloading google_api_python_client-2.174.0-py3-none-any.whl (13.7 MB)
   ---------------------------------------- 13.7/13.7 MB 16.2 MB/s eta 0:00:00
Downloading google_cloud_aiplatform-1.100.0-py2.py3-none-any.whl (7.8 MB)
   ---------------------------------------- 7.8/7.8 MB 18.0 MB/s eta 0:00:00
Downloading google_cloud_secret_manager-2.24.0-py3-none-any.whl (218 kB)
Downloading google_cloud_speech-2.33.0-py3-none-any.whl (335 kB)
Downloading google_cloud_storage-2.19.0-py2.py3-none-any.whl (131 kB)
Downloading google_genai-1.23.0-py3-none-any.whl (223 kB)
Downloading graphviz-0.21-py3-none-any.whl (47 kB)
Downloading opentelemetry_api-1.34.1-py3-none-any.whl (65 kB)
Downloading opentelemetry_exporter_gcp_trace-1.9.0-py3-none-any.whl (13 kB)
Downloading opentelemetry_resourcedetector_gcp-1.9.0a0-py3-none-any.whl (20 kB)
Downloading opentelemetry_sdk-1.34.1-py3-none-any.whl (118 kB)
Downloading opentelemetry_semantic_conventions-0.55b1-py3-none-any.whl (196 kB)
Downloading pydantic-2.11.7-py3-none-any.whl (444 kB)
Downloading pydantic_core-2.33.2-cp39-cp39-win_amd64.whl (2.0 MB)
   ---------------------------------------- 2.0/2.0 MB 15.5 MB/s eta 0:00:00
Downloading requests-2.32.4-py3-none-any.whl (64 kB)
Using cached tzlocal-5.3.1-py3-none-any.whl (18 kB)
Downloading websockets-15.0.1-cp39-cp39-win_amd64.whl (176 kB)
Downloading cloudpickle-3.1.1-py3-none-any.whl (20 kB)
Using cached docstring_parser-0.16-py3-none-any.whl (36 kB)
Downloading google_auth_httplib2-0.2.0-py2.py3-none-any.whl (9.3 kB)
Downloading google_cloud_bigquery-3.34.0-py3-none-any.whl (253 kB)
Downloading google_cloud_core-2.4.3-py2.py3-none-any.whl (29 kB)
Downloading google_cloud_logging-3.12.1-py2.py3-none-any.whl (229 kB)
Downloading google_cloud_resource_manager-1.14.2-py3-none-any.whl (394 kB)
Downloading google_cloud_trace-1.16.2-py3-none-any.whl (103 kB)
Downloading google_crc32c-1.7.1-cp39-cp39-win_amd64.whl (33 kB)
Downloading google_resumable_media-2.7.2-py2.py3-none-any.whl (81 kB)
Downloading grpc_google_iam_v1-0.14.2-py3-none-any.whl (19 kB)
Downloading httplib2-0.22.0-py3-none-any.whl (96 kB)
Using cached packaging-25.0-py3-none-any.whl (66 kB)
Using cached shapely-2.0.7-cp39-cp39-win_amd64.whl (1.4 MB)
Downloading typing_inspection-0.4.1-py3-none-any.whl (14 kB)
Downloading uritemplate-4.2.0-py3-none-any.whl (11 kB)
Downloading cryptography-45.0.4-cp37-abi3-win_amd64.whl (3.4 MB)
   ---------------------------------------- 3.4/3.4 MB 16.8 MB/s eta 0:00:00
Using cached cffi-1.17.1-cp39-cp39-win_amd64.whl (181 kB)
Downloading google_cloud_appengine_logging-1.6.2-py3-none-any.whl (16 kB)
Downloading google_cloud_audit_log-0.3.2-py3-none-any.whl (32 kB)
Using cached pyparsing-3.2.3-py3-none-any.whl (111 kB)
Using cached pycparser-2.22-py3-none-any.whl (117 kB)
Installing collected packages: websockets, uritemplate, tzlocal, typing-inspection, shapely, requests, pyparsing, pydantic-core, pycparser, packaging, graphviz, google-crc32c, docstring_parser, cloudpickle, pydantic, opentelemetry-api, httplib2, google-resumable-media, google-cloud-audit-log, cffi, opentelemetry-semantic-conventions, grpc-google-iam-v1, google-genai, google-auth-httplib2, cryptography, opentelemetry-sdk, google-cloud-core, google-api-python-client, authlib, opentelemetry-resourcedetector-gcp, google-cloud-trace, google-cloud-storage, google-cloud-speech, google-cloud-secret-manager, google-cloud-resource-manager, google-cloud-bigquery, google-cloud-appengine-logging, opentelemetry-exporter-gcp-trace, google-cloud-logging, google-cloud-aiplatform, google-adk
  Attempting uninstall: requests
    Found existing installation: requests 2.32.3
    Uninstalling requests-2.32.3:
      Successfully uninstalled requests-2.32.3
  Attempting uninstall: pydantic-core
    Found existing installation: pydantic_core 2.27.2
    Uninstalling pydantic_core-2.27.2:
      Successfully uninstalled pydantic_core-2.27.2
  Attempting uninstall: packaging
    Found existing installation: packaging 23.2
    Uninstalling packaging-23.2:
      Successfully uninstalled packaging-23.2
  Attempting uninstall: pydantic
    Found existing installation: pydantic 2.10.6
    Uninstalling pydantic-2.10.6:
      Successfully uninstalled pydantic-2.10.6
Successfully installed authlib-1.6.0 cffi-1.17.1 cloudpickle-3.1.1 cryptography-45.0.4 docstring_parser-0.16 google-adk-1.5.0 google-api-python-client-2.174.0 google-auth-httplib2-0.2.0 google-cloud-aiplatform-1.100.0 google-cloud-appengine-logging-1.6.2 google-cloud-audit-log-0.3.2 google-cloud-bigquery-3.34.0 google-cloud-core-2.4.3 google-cloud-logging-3.12.1 google-cloud-resource-manager-1.14.2 google-cloud-secret-manager-2.24.0 google-cloud-speech-2.33.0 google-cloud-storage-2.19.0 google-cloud-trace-1.16.2 google-crc32c-1.7.1 google-genai-1.23.0 google-resumable-media-2.7.2 graphviz-0.21 grpc-google-iam-v1-0.14.2 httplib2-0.22.0 opentelemetry-api-1.34.1 opentelemetry-exporter-gcp-trace-1.9.0 opentelemetry-resourcedetector-gcp-1.9.0a0 opentelemetry-sdk-1.34.1 opentelemetry-semantic-conventions-0.55b1 packaging-25.0 pycparser-2.22 pydantic-2.11.7 pydantic-core-2.33.2 pyparsing-3.2.3 requests-2.32.4 shapely-2.0.7 typing-inspection-0.4.1 tzlocal-5.3.1 uritemplate-4.2.0 websockets-15.0.1
