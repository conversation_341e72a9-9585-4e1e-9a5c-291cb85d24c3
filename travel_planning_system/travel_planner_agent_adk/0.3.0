Requirement already satisfied: langchain-groq in c:\users\<USER>\appdata\local\programs\python\python39\lib\site-packages (0.3.4)
Collecting langchain-core<1.0.0,>=0.3.66 (from langchain-groq)
  Using cached langchain_core-0.3.66-py3-none-any.whl.metadata (5.8 kB)
Requirement already satisfied: groq<1,>=0.28.0 in c:\users\<USER>\appdata\local\programs\python\python39\lib\site-packages (from langchain-groq) (0.29.0)
Requirement already satisfied: anyio<5,>=3.5.0 in c:\users\<USER>\appdata\local\programs\python\python39\lib\site-packages (from groq<1,>=0.28.0->langchain-groq) (4.8.0)
Requirement already satisfied: distro<2,>=1.7.0 in c:\users\<USER>\appdata\local\programs\python\python39\lib\site-packages (from groq<1,>=0.28.0->langchain-groq) (1.9.0)
Requirement already satisfied: httpx<1,>=0.23.0 in c:\users\<USER>\appdata\local\programs\python\python39\lib\site-packages (from groq<1,>=0.28.0->langchain-groq) (0.28.1)
Requirement already satisfied: pydantic<3,>=1.9.0 in c:\users\<USER>\appdata\local\programs\python\python39\lib\site-packages (from groq<1,>=0.28.0->langchain-groq) (2.10.6)
Requirement already satisfied: sniffio in c:\users\<USER>\appdata\local\programs\python\python39\lib\site-packages (from groq<1,>=0.28.0->langchain-groq) (1.3.1)
Requirement already satisfied: typing-extensions<5,>=4.10 in c:\users\<USER>\appdata\local\programs\python\python39\lib\site-packages (from groq<1,>=0.28.0->langchain-groq) (4.12.2)
Collecting langsmith>=0.3.45 (from langchain-core<1.0.0,>=0.3.66->langchain-groq)
  Using cached langsmith-0.4.4-py3-none-any.whl.metadata (15 kB)
Requirement already satisfied: tenacity!=8.4.0,<10.0.0,>=8.1.0 in c:\users\<USER>\appdata\local\programs\python\python39\lib\site-packages (from langchain-core<1.0.0,>=0.3.66->langchain-groq) (8.5.0)
Requirement already satisfied: jsonpatch<2.0,>=1.33 in c:\users\<USER>\appdata\local\programs\python\python39\lib\site-packages (from langchain-core<1.0.0,>=0.3.66->langchain-groq) (1.33)
Requirement already satisfied: PyYAML>=5.3 in c:\users\<USER>\appdata\local\programs\python\python39\lib\site-packages (from langchain-core<1.0.0,>=0.3.66->langchain-groq) (6.0.2)
Requirement already satisfied: packaging<25,>=23.2 in c:\users\<USER>\appdata\local\programs\python\python39\lib\site-packages (from langchain-core<1.0.0,>=0.3.66->langchain-groq) (23.2)
Requirement already satisfied: exceptiongroup>=1.0.2 in c:\users\<USER>\appdata\local\programs\python\python39\lib\site-packages (from anyio<5,>=3.5.0->groq<1,>=0.28.0->langchain-groq) (1.2.2)
Requirement already satisfied: idna>=2.8 in c:\users\<USER>\appdata\local\programs\python\python39\lib\site-packages (from anyio<5,>=3.5.0->groq<1,>=0.28.0->langchain-groq) (3.10)
Requirement already satisfied: certifi in c:\users\<USER>\appdata\local\programs\python\python39\lib\site-packages (from httpx<1,>=0.23.0->groq<1,>=0.28.0->langchain-groq) (2025.1.31)
Requirement already satisfied: httpcore==1.* in c:\users\<USER>\appdata\local\programs\python\python39\lib\site-packages (from httpx<1,>=0.23.0->groq<1,>=0.28.0->langchain-groq) (1.0.7)
Requirement already satisfied: h11<0.15,>=0.13 in c:\users\<USER>\appdata\local\programs\python\python39\lib\site-packages (from httpcore==1.*->httpx<1,>=0.23.0->groq<1,>=0.28.0->langchain-groq) (0.14.0)
Requirement already satisfied: jsonpointer>=1.9 in c:\users\<USER>\appdata\local\programs\python\python39\lib\site-packages (from jsonpatch<2.0,>=1.33->langchain-core<1.0.0,>=0.3.66->langchain-groq) (3.0.0)
Requirement already satisfied: orjson<4.0.0,>=3.9.14 in c:\users\<USER>\appdata\local\programs\python\python39\lib\site-packages (from langsmith>=0.3.45->langchain-core<1.0.0,>=0.3.66->langchain-groq) (3.10.15)
Requirement already satisfied: requests<3,>=2 in c:\users\<USER>\appdata\local\programs\python\python39\lib\site-packages (from langsmith>=0.3.45->langchain-core<1.0.0,>=0.3.66->langchain-groq) (2.32.3)
Requirement already satisfied: requests-toolbelt<2.0.0,>=1.0.0 in c:\users\<USER>\appdata\local\programs\python\python39\lib\site-packages (from langsmith>=0.3.45->langchain-core<1.0.0,>=0.3.66->langchain-groq) (1.0.0)
Requirement already satisfied: zstandard<0.24.0,>=0.23.0 in c:\users\<USER>\appdata\local\programs\python\python39\lib\site-packages (from langsmith>=0.3.45->langchain-core<1.0.0,>=0.3.66->langchain-groq) (0.23.0)
Requirement already satisfied: annotated-types>=0.6.0 in c:\users\<USER>\appdata\local\programs\python\python39\lib\site-packages (from pydantic<3,>=1.9.0->groq<1,>=0.28.0->langchain-groq) (0.7.0)
Requirement already satisfied: pydantic-core==2.27.2 in c:\users\<USER>\appdata\local\programs\python\python39\lib\site-packages (from pydantic<3,>=1.9.0->groq<1,>=0.28.0->langchain-groq) (2.27.2)
Requirement already satisfied: charset-normalizer<4,>=2 in c:\users\<USER>\appdata\local\programs\python\python39\lib\site-packages (from requests<3,>=2->langsmith>=0.3.45->langchain-core<1.0.0,>=0.3.66->langchain-groq) (3.4.1)
Requirement already satisfied: urllib3<3,>=1.21.1 in c:\users\<USER>\appdata\local\programs\python\python39\lib\site-packages (from requests<3,>=2->langsmith>=0.3.45->langchain-core<1.0.0,>=0.3.66->langchain-groq) (2.3.0)
Using cached langchain_core-0.3.66-py3-none-any.whl (438 kB)
Using cached langsmith-0.4.4-py3-none-any.whl (367 kB)
Installing collected packages: langsmith, langchain-core
  Attempting uninstall: langsmith
    Found existing installation: langsmith 0.1.147
    Uninstalling langsmith-0.1.147:
      Successfully uninstalled langsmith-0.1.147
  Attempting uninstall: langchain-core
    Found existing installation: langchain-core 0.1.53
    Uninstalling langchain-core-0.1.53:
      Successfully uninstalled langchain-core-0.1.53
Successfully installed langchain-core-0.3.66 langsmith-0.4.4
