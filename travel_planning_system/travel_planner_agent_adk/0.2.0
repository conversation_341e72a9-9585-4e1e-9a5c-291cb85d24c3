Requirement already satisfied: langchain-community in c:\users\<USER>\appdata\local\programs\python\python39\lib\site-packages (0.0.38)
Requirement already satisfied: PyYAML>=5.3 in c:\users\<USER>\appdata\local\programs\python\python39\lib\site-packages (from langchain-community) (6.0.2)
Requirement already satisfied: SQLAlchemy<3,>=1.4 in c:\users\<USER>\appdata\local\programs\python\python39\lib\site-packages (from langchain-community) (2.0.38)
Requirement already satisfied: aiohttp<4.0.0,>=3.8.3 in c:\users\<USER>\appdata\local\programs\python\python39\lib\site-packages (from langchain-community) (3.11.13)
Requirement already satisfied: dataclasses-json<0.7,>=0.5.7 in c:\users\<USER>\appdata\local\programs\python\python39\lib\site-packages (from langchain-community) (0.6.7)
Requirement already satisfied: langchain-core<0.2.0,>=0.1.52 in c:\users\<USER>\appdata\local\programs\python\python39\lib\site-packages (from langchain-community) (0.1.53)
Requirement already satisfied: langsmith<0.2.0,>=0.1.0 in c:\users\<USER>\appdata\local\programs\python\python39\lib\site-packages (from langchain-community) (0.1.147)
Requirement already satisfied: numpy<2,>=1 in c:\users\<USER>\appdata\local\programs\python\python39\lib\site-packages (from langchain-community) (1.26.4)
Requirement already satisfied: requests<3,>=2 in c:\users\<USER>\appdata\local\programs\python\python39\lib\site-packages (from langchain-community) (2.32.3)
Requirement already satisfied: tenacity<9.0.0,>=8.1.0 in c:\users\<USER>\appdata\local\programs\python\python39\lib\site-packages (from langchain-community) (8.5.0)
Requirement already satisfied: aiohappyeyeballs>=2.3.0 in c:\users\<USER>\appdata\local\programs\python\python39\lib\site-packages (from aiohttp<4.0.0,>=3.8.3->langchain-community) (2.4.6)
Requirement already satisfied: aiosignal>=1.1.2 in c:\users\<USER>\appdata\local\programs\python\python39\lib\site-packages (from aiohttp<4.0.0,>=3.8.3->langchain-community) (1.3.2)
Requirement already satisfied: async-timeout<6.0,>=4.0 in c:\users\<USER>\appdata\local\programs\python\python39\lib\site-packages (from aiohttp<4.0.0,>=3.8.3->langchain-community) (4.0.3)
Requirement already satisfied: attrs>=17.3.0 in c:\users\<USER>\appdata\local\programs\python\python39\lib\site-packages (from aiohttp<4.0.0,>=3.8.3->langchain-community) (25.1.0)
Requirement already satisfied: frozenlist>=1.1.1 in c:\users\<USER>\appdata\local\programs\python\python39\lib\site-packages (from aiohttp<4.0.0,>=3.8.3->langchain-community) (1.5.0)
Requirement already satisfied: multidict<7.0,>=4.5 in c:\users\<USER>\appdata\local\programs\python\python39\lib\site-packages (from aiohttp<4.0.0,>=3.8.3->langchain-community) (6.1.0)
Requirement already satisfied: propcache>=0.2.0 in c:\users\<USER>\appdata\local\programs\python\python39\lib\site-packages (from aiohttp<4.0.0,>=3.8.3->langchain-community) (0.3.0)
Requirement already satisfied: yarl<2.0,>=1.17.0 in c:\users\<USER>\appdata\local\programs\python\python39\lib\site-packages (from aiohttp<4.0.0,>=3.8.3->langchain-community) (1.18.3)
Requirement already satisfied: marshmallow<4.0.0,>=3.18.0 in c:\users\<USER>\appdata\local\programs\python\python39\lib\site-packages (from dataclasses-json<0.7,>=0.5.7->langchain-community) (3.26.1)
Requirement already satisfied: typing-inspect<1,>=0.4.0 in c:\users\<USER>\appdata\local\programs\python\python39\lib\site-packages (from dataclasses-json<0.7,>=0.5.7->langchain-community) (0.9.0)
Requirement already satisfied: jsonpatch<2.0,>=1.33 in c:\users\<USER>\appdata\local\programs\python\python39\lib\site-packages (from langchain-core<0.2.0,>=0.1.52->langchain-community) (1.33)
Requirement already satisfied: packaging<24.0,>=23.2 in c:\users\<USER>\appdata\local\programs\python\python39\lib\site-packages (from langchain-core<0.2.0,>=0.1.52->langchain-community) (23.2)
Requirement already satisfied: pydantic<3,>=1 in c:\users\<USER>\appdata\local\programs\python\python39\lib\site-packages (from langchain-core<0.2.0,>=0.1.52->langchain-community) (2.10.6)
Requirement already satisfied: httpx<1,>=0.23.0 in c:\users\<USER>\appdata\local\programs\python\python39\lib\site-packages (from langsmith<0.2.0,>=0.1.0->langchain-community) (0.28.1)
Requirement already satisfied: orjson<4.0.0,>=3.9.14 in c:\users\<USER>\appdata\local\programs\python\python39\lib\site-packages (from langsmith<0.2.0,>=0.1.0->langchain-community) (3.10.15)
Requirement already satisfied: requests-toolbelt<2.0.0,>=1.0.0 in c:\users\<USER>\appdata\local\programs\python\python39\lib\site-packages (from langsmith<0.2.0,>=0.1.0->langchain-community) (1.0.0)
Requirement already satisfied: charset-normalizer<4,>=2 in c:\users\<USER>\appdata\local\programs\python\python39\lib\site-packages (from requests<3,>=2->langchain-community) (3.4.1)
Requirement already satisfied: idna<4,>=2.5 in c:\users\<USER>\appdata\local\programs\python\python39\lib\site-packages (from requests<3,>=2->langchain-community) (3.10)
Requirement already satisfied: urllib3<3,>=1.21.1 in c:\users\<USER>\appdata\local\programs\python\python39\lib\site-packages (from requests<3,>=2->langchain-community) (2.3.0)
Requirement already satisfied: certifi>=2017.4.17 in c:\users\<USER>\appdata\local\programs\python\python39\lib\site-packages (from requests<3,>=2->langchain-community) (2025.1.31)
Requirement already satisfied: greenlet!=0.4.17 in c:\users\<USER>\appdata\local\programs\python\python39\lib\site-packages (from SQLAlchemy<3,>=1.4->langchain-community) (3.1.1)
Requirement already satisfied: typing-extensions>=4.6.0 in c:\users\<USER>\appdata\local\programs\python\python39\lib\site-packages (from SQLAlchemy<3,>=1.4->langchain-community) (4.12.2)
Requirement already satisfied: anyio in c:\users\<USER>\appdata\local\programs\python\python39\lib\site-packages (from httpx<1,>=0.23.0->langsmith<0.2.0,>=0.1.0->langchain-community) (4.8.0)
Requirement already satisfied: httpcore==1.* in c:\users\<USER>\appdata\local\programs\python\python39\lib\site-packages (from httpx<1,>=0.23.0->langsmith<0.2.0,>=0.1.0->langchain-community) (1.0.7)
Requirement already satisfied: h11<0.15,>=0.13 in c:\users\<USER>\appdata\local\programs\python\python39\lib\site-packages (from httpcore==1.*->httpx<1,>=0.23.0->langsmith<0.2.0,>=0.1.0->langchain-community) (0.14.0)
Requirement already satisfied: jsonpointer>=1.9 in c:\users\<USER>\appdata\local\programs\python\python39\lib\site-packages (from jsonpatch<2.0,>=1.33->langchain-core<0.2.0,>=0.1.52->langchain-community) (3.0.0)
Requirement already satisfied: annotated-types>=0.6.0 in c:\users\<USER>\appdata\local\programs\python\python39\lib\site-packages (from pydantic<3,>=1->langchain-core<0.2.0,>=0.1.52->langchain-community) (0.7.0)
Requirement already satisfied: pydantic-core==2.27.2 in c:\users\<USER>\appdata\local\programs\python\python39\lib\site-packages (from pydantic<3,>=1->langchain-core<0.2.0,>=0.1.52->langchain-community) (2.27.2)
Requirement already satisfied: mypy-extensions>=0.3.0 in c:\users\<USER>\appdata\local\programs\python\python39\lib\site-packages (from typing-inspect<1,>=0.4.0->dataclasses-json<0.7,>=0.5.7->langchain-community) (1.0.0)
Requirement already satisfied: exceptiongroup>=1.0.2 in c:\users\<USER>\appdata\local\programs\python\python39\lib\site-packages (from anyio->httpx<1,>=0.23.0->langsmith<0.2.0,>=0.1.0->langchain-community) (1.2.2)
Requirement already satisfied: sniffio>=1.1 in c:\users\<USER>\appdata\local\programs\python\python39\lib\site-packages (from anyio->httpx<1,>=0.23.0->langsmith<0.2.0,>=0.1.0->langchain-community) (1.3.1)
