Requirement already satisfied: langchain-google-genai in c:\users\<USER>\appdata\local\programs\python\python39\lib\site-packages (2.1.5)
Requirement already satisfied: filetype<2.0.0,>=1.2.0 in c:\users\<USER>\appdata\local\programs\python\python39\lib\site-packages (from langchain-google-genai) (1.2.0)
Requirement already satisfied: google-ai-generativelanguage<0.7.0,>=0.6.18 in c:\users\<USER>\appdata\local\programs\python\python39\lib\site-packages (from langchain-google-genai) (0.6.18)
Requirement already satisfied: langchain-core<0.4.0,>=0.3.62 in c:\users\<USER>\appdata\local\programs\python\python39\lib\site-packages (from langchain-google-genai) (0.3.66)
Requirement already satisfied: pydantic<3,>=2 in c:\users\<USER>\appdata\local\programs\python\python39\lib\site-packages (from langchain-google-genai) (2.10.6)
Requirement already satisfied: google-api-core!=2.0.*,!=2.1.*,!=2.10.*,!=2.2.*,!=2.3.*,!=2.4.*,!=2.5.*,!=2.6.*,!=2.7.*,!=2.8.*,!=2.9.*,<3.0.0,>=1.34.1 in c:\users\<USER>\appdata\local\programs\python\python39\lib\site-packages (from google-api-core[grpc]!=2.0.*,!=2.1.*,!=2.10.*,!=2.2.*,!=2.3.*,!=2.4.*,!=2.5.*,!=2.6.*,!=2.7.*,!=2.8.*,!=2.9.*,<3.0.0,>=1.34.1->google-ai-generativelanguage<0.7.0,>=0.6.18->langchain-google-genai) (2.25.1)
Requirement already satisfied: google-auth!=2.24.0,!=2.25.0,<3.0.0,>=2.14.1 in c:\users\<USER>\appdata\local\programs\python\python39\lib\site-packages (from google-ai-generativelanguage<0.7.0,>=0.6.18->langchain-google-genai) (2.40.3)
Requirement already satisfied: proto-plus<2.0.0,>=1.22.3 in c:\users\<USER>\appdata\local\programs\python\python39\lib\site-packages (from google-ai-generativelanguage<0.7.0,>=0.6.18->langchain-google-genai) (1.26.1)
Requirement already satisfied: protobuf!=4.21.0,!=4.21.1,!=4.21.2,!=4.21.3,!=4.21.4,!=4.21.5,<7.0.0,>=3.20.2 in c:\users\<USER>\appdata\local\programs\python\python39\lib\site-packages (from google-ai-generativelanguage<0.7.0,>=0.6.18->langchain-google-genai) (6.31.1)
Requirement already satisfied: langsmith>=0.3.45 in c:\users\<USER>\appdata\local\programs\python\python39\lib\site-packages (from langchain-core<0.4.0,>=0.3.62->langchain-google-genai) (0.4.4)
Requirement already satisfied: tenacity!=8.4.0,<10.0.0,>=8.1.0 in c:\users\<USER>\appdata\local\programs\python\python39\lib\site-packages (from langchain-core<0.4.0,>=0.3.62->langchain-google-genai) (8.5.0)
Requirement already satisfied: jsonpatch<2.0,>=1.33 in c:\users\<USER>\appdata\local\programs\python\python39\lib\site-packages (from langchain-core<0.4.0,>=0.3.62->langchain-google-genai) (1.33)
Requirement already satisfied: PyYAML>=5.3 in c:\users\<USER>\appdata\local\programs\python\python39\lib\site-packages (from langchain-core<0.4.0,>=0.3.62->langchain-google-genai) (6.0.2)
Requirement already satisfied: packaging<25,>=23.2 in c:\users\<USER>\appdata\local\programs\python\python39\lib\site-packages (from langchain-core<0.4.0,>=0.3.62->langchain-google-genai) (23.2)
Requirement already satisfied: typing-extensions>=4.7 in c:\users\<USER>\appdata\local\programs\python\python39\lib\site-packages (from langchain-core<0.4.0,>=0.3.62->langchain-google-genai) (4.12.2)
Requirement already satisfied: annotated-types>=0.6.0 in c:\users\<USER>\appdata\local\programs\python\python39\lib\site-packages (from pydantic<3,>=2->langchain-google-genai) (0.7.0)
Requirement already satisfied: pydantic-core==2.27.2 in c:\users\<USER>\appdata\local\programs\python\python39\lib\site-packages (from pydantic<3,>=2->langchain-google-genai) (2.27.2)
Requirement already satisfied: googleapis-common-protos<2.0.0,>=1.56.2 in c:\users\<USER>\appdata\local\programs\python\python39\lib\site-packages (from google-api-core!=2.0.*,!=2.1.*,!=2.10.*,!=2.2.*,!=2.3.*,!=2.4.*,!=2.5.*,!=2.6.*,!=2.7.*,!=2.8.*,!=2.9.*,<3.0.0,>=1.34.1->google-api-core[grpc]!=2.0.*,!=2.1.*,!=2.10.*,!=2.2.*,!=2.3.*,!=2.4.*,!=2.5.*,!=2.6.*,!=2.7.*,!=2.8.*,!=2.9.*,<3.0.0,>=1.34.1->google-ai-generativelanguage<0.7.0,>=0.6.18->langchain-google-genai) (1.70.0)
Requirement already satisfied: requests<3.0.0,>=2.18.0 in c:\users\<USER>\appdata\local\programs\python\python39\lib\site-packages (from google-api-core!=2.0.*,!=2.1.*,!=2.10.*,!=2.2.*,!=2.3.*,!=2.4.*,!=2.5.*,!=2.6.*,!=2.7.*,!=2.8.*,!=2.9.*,<3.0.0,>=1.34.1->google-api-core[grpc]!=2.0.*,!=2.1.*,!=2.10.*,!=2.2.*,!=2.3.*,!=2.4.*,!=2.5.*,!=2.6.*,!=2.7.*,!=2.8.*,!=2.9.*,<3.0.0,>=1.34.1->google-ai-generativelanguage<0.7.0,>=0.6.18->langchain-google-genai) (2.32.3)
Requirement already satisfied: grpcio<2.0.0,>=1.33.2 in c:\users\<USER>\appdata\local\programs\python\python39\lib\site-packages (from google-api-core[grpc]!=2.0.*,!=2.1.*,!=2.10.*,!=2.2.*,!=2.3.*,!=2.4.*,!=2.5.*,!=2.6.*,!=2.7.*,!=2.8.*,!=2.9.*,<3.0.0,>=1.34.1->google-ai-generativelanguage<0.7.0,>=0.6.18->langchain-google-genai) (1.73.1)
Requirement already satisfied: grpcio-status<2.0.0,>=1.33.2 in c:\users\<USER>\appdata\local\programs\python\python39\lib\site-packages (from google-api-core[grpc]!=2.0.*,!=2.1.*,!=2.10.*,!=2.2.*,!=2.3.*,!=2.4.*,!=2.5.*,!=2.6.*,!=2.7.*,!=2.8.*,!=2.9.*,<3.0.0,>=1.34.1->google-ai-generativelanguage<0.7.0,>=0.6.18->langchain-google-genai) (1.73.1)
Requirement already satisfied: cachetools<6.0,>=2.0.0 in c:\users\<USER>\appdata\local\programs\python\python39\lib\site-packages (from google-auth!=2.24.0,!=2.25.0,<3.0.0,>=2.14.1->google-ai-generativelanguage<0.7.0,>=0.6.18->langchain-google-genai) (5.5.2)
Requirement already satisfied: pyasn1-modules>=0.2.1 in c:\users\<USER>\appdata\local\programs\python\python39\lib\site-packages (from google-auth!=2.24.0,!=2.25.0,<3.0.0,>=2.14.1->google-ai-generativelanguage<0.7.0,>=0.6.18->langchain-google-genai) (0.4.2)
Requirement already satisfied: rsa<5,>=3.1.4 in c:\users\<USER>\appdata\local\programs\python\python39\lib\site-packages (from google-auth!=2.24.0,!=2.25.0,<3.0.0,>=2.14.1->google-ai-generativelanguage<0.7.0,>=0.6.18->langchain-google-genai) (4.9.1)
Requirement already satisfied: jsonpointer>=1.9 in c:\users\<USER>\appdata\local\programs\python\python39\lib\site-packages (from jsonpatch<2.0,>=1.33->langchain-core<0.4.0,>=0.3.62->langchain-google-genai) (3.0.0)
Requirement already satisfied: httpx<1,>=0.23.0 in c:\users\<USER>\appdata\local\programs\python\python39\lib\site-packages (from langsmith>=0.3.45->langchain-core<0.4.0,>=0.3.62->langchain-google-genai) (0.28.1)
Requirement already satisfied: orjson<4.0.0,>=3.9.14 in c:\users\<USER>\appdata\local\programs\python\python39\lib\site-packages (from langsmith>=0.3.45->langchain-core<0.4.0,>=0.3.62->langchain-google-genai) (3.10.15)
Requirement already satisfied: requests-toolbelt<2.0.0,>=1.0.0 in c:\users\<USER>\appdata\local\programs\python\python39\lib\site-packages (from langsmith>=0.3.45->langchain-core<0.4.0,>=0.3.62->langchain-google-genai) (1.0.0)
Requirement already satisfied: zstandard<0.24.0,>=0.23.0 in c:\users\<USER>\appdata\local\programs\python\python39\lib\site-packages (from langsmith>=0.3.45->langchain-core<0.4.0,>=0.3.62->langchain-google-genai) (0.23.0)
Requirement already satisfied: anyio in c:\users\<USER>\appdata\local\programs\python\python39\lib\site-packages (from httpx<1,>=0.23.0->langsmith>=0.3.45->langchain-core<0.4.0,>=0.3.62->langchain-google-genai) (4.8.0)
Requirement already satisfied: certifi in c:\users\<USER>\appdata\local\programs\python\python39\lib\site-packages (from httpx<1,>=0.23.0->langsmith>=0.3.45->langchain-core<0.4.0,>=0.3.62->langchain-google-genai) (2025.1.31)
Requirement already satisfied: httpcore==1.* in c:\users\<USER>\appdata\local\programs\python\python39\lib\site-packages (from httpx<1,>=0.23.0->langsmith>=0.3.45->langchain-core<0.4.0,>=0.3.62->langchain-google-genai) (1.0.7)
Requirement already satisfied: idna in c:\users\<USER>\appdata\local\programs\python\python39\lib\site-packages (from httpx<1,>=0.23.0->langsmith>=0.3.45->langchain-core<0.4.0,>=0.3.62->langchain-google-genai) (3.10)
Requirement already satisfied: h11<0.15,>=0.13 in c:\users\<USER>\appdata\local\programs\python\python39\lib\site-packages (from httpcore==1.*->httpx<1,>=0.23.0->langsmith>=0.3.45->langchain-core<0.4.0,>=0.3.62->langchain-google-genai) (0.14.0)
Requirement already satisfied: pyasn1<0.7.0,>=0.6.1 in c:\users\<USER>\appdata\local\programs\python\python39\lib\site-packages (from pyasn1-modules>=0.2.1->google-auth!=2.24.0,!=2.25.0,<3.0.0,>=2.14.1->google-ai-generativelanguage<0.7.0,>=0.6.18->langchain-google-genai) (0.6.1)
Requirement already satisfied: charset-normalizer<4,>=2 in c:\users\<USER>\appdata\local\programs\python\python39\lib\site-packages (from requests<3.0.0,>=2.18.0->google-api-core!=2.0.*,!=2.1.*,!=2.10.*,!=2.2.*,!=2.3.*,!=2.4.*,!=2.5.*,!=2.6.*,!=2.7.*,!=2.8.*,!=2.9.*,<3.0.0,>=1.34.1->google-api-core[grpc]!=2.0.*,!=2.1.*,!=2.10.*,!=2.2.*,!=2.3.*,!=2.4.*,!=2.5.*,!=2.6.*,!=2.7.*,!=2.8.*,!=2.9.*,<3.0.0,>=1.34.1->google-ai-generativelanguage<0.7.0,>=0.6.18->langchain-google-genai) (3.4.1)
Requirement already satisfied: urllib3<3,>=1.21.1 in c:\users\<USER>\appdata\local\programs\python\python39\lib\site-packages (from requests<3.0.0,>=2.18.0->google-api-core!=2.0.*,!=2.1.*,!=2.10.*,!=2.2.*,!=2.3.*,!=2.4.*,!=2.5.*,!=2.6.*,!=2.7.*,!=2.8.*,!=2.9.*,<3.0.0,>=1.34.1->google-api-core[grpc]!=2.0.*,!=2.1.*,!=2.10.*,!=2.2.*,!=2.3.*,!=2.4.*,!=2.5.*,!=2.6.*,!=2.7.*,!=2.8.*,!=2.9.*,<3.0.0,>=1.34.1->google-ai-generativelanguage<0.7.0,>=0.6.18->langchain-google-genai) (2.3.0)
Requirement already satisfied: exceptiongroup>=1.0.2 in c:\users\<USER>\appdata\local\programs\python\python39\lib\site-packages (from anyio->httpx<1,>=0.23.0->langsmith>=0.3.45->langchain-core<0.4.0,>=0.3.62->langchain-google-genai) (1.2.2)
Requirement already satisfied: sniffio>=1.1 in c:\users\<USER>\appdata\local\programs\python\python39\lib\site-packages (from anyio->httpx<1,>=0.23.0->langsmith>=0.3.45->langchain-core<0.4.0,>=0.3.62->langchain-google-genai) (1.3.1)
