Requirement already satisfied: crewai in c:\users\<USER>\appdata\local\programs\python\python39\lib\site-packages (0.1.32)
Collecting langchain<0.2.0,>=0.1.0 (from crewai)
  Using cached langchain-0.1.20-py3-none-any.whl.metadata (13 kB)
Requirement already satisfied: langchain-openai<0.0.3,>=0.0.2 in c:\users\<USER>\appdata\local\programs\python\python39\lib\site-packages (from crewai) (0.0.2.post1)
Requirement already satisfied: openai<2.0.0,>=1.7.1 in c:\users\<USER>\appdata\local\programs\python\python39\lib\site-packages (from crewai) (1.75.0)
Requirement already satisfied: pydantic<3.0.0,>=2.4.2 in c:\users\<USER>\appdata\local\programs\python\python39\lib\site-packages (from crewai) (2.10.6)
Requirement already satisfied: PyYAML>=5.3 in c:\users\<USER>\appdata\local\programs\python\python39\lib\site-packages (from langchain<0.2.0,>=0.1.0->crewai) (6.0.2)
Requirement already satisfied: SQLAlchemy<3,>=1.4 in c:\users\<USER>\appdata\local\programs\python\python39\lib\site-packages (from langchain<0.2.0,>=0.1.0->crewai) (2.0.38)
Requirement already satisfied: aiohttp<4.0.0,>=3.8.3 in c:\users\<USER>\appdata\local\programs\python\python39\lib\site-packages (from langchain<0.2.0,>=0.1.0->crewai) (3.11.13)
Requirement already satisfied: async-timeout<5.0.0,>=4.0.0 in c:\users\<USER>\appdata\local\programs\python\python39\lib\site-packages (from langchain<0.2.0,>=0.1.0->crewai) (4.0.3)
Requirement already satisfied: dataclasses-json<0.7,>=0.5.7 in c:\users\<USER>\appdata\local\programs\python\python39\lib\site-packages (from langchain<0.2.0,>=0.1.0->crewai) (0.6.7)
Collecting langchain-community<0.1,>=0.0.38 (from langchain<0.2.0,>=0.1.0->crewai)
  Using cached langchain_community-0.0.38-py3-none-any.whl.metadata (8.7 kB)
Collecting langchain-core<0.2.0,>=0.1.52 (from langchain<0.2.0,>=0.1.0->crewai)
  Using cached langchain_core-0.1.53-py3-none-any.whl.metadata (5.9 kB)
Collecting langchain-text-splitters<0.1,>=0.0.1 (from langchain<0.2.0,>=0.1.0->crewai)
  Using cached langchain_text_splitters-0.0.2-py3-none-any.whl.metadata (2.2 kB)
Collecting langsmith<0.2.0,>=0.1.17 (from langchain<0.2.0,>=0.1.0->crewai)
  Using cached langsmith-0.1.147-py3-none-any.whl.metadata (14 kB)
Requirement already satisfied: numpy<2,>=1 in c:\users\<USER>\appdata\local\programs\python\python39\lib\site-packages (from langchain<0.2.0,>=0.1.0->crewai) (1.26.4)
Requirement already satisfied: requests<3,>=2 in c:\users\<USER>\appdata\local\programs\python\python39\lib\site-packages (from langchain<0.2.0,>=0.1.0->crewai) (2.32.3)
Requirement already satisfied: tenacity<9.0.0,>=8.1.0 in c:\users\<USER>\appdata\local\programs\python\python39\lib\site-packages (from langchain<0.2.0,>=0.1.0->crewai) (8.5.0)
Requirement already satisfied: tiktoken<0.6.0,>=0.5.2 in c:\users\<USER>\appdata\local\programs\python\python39\lib\site-packages (from langchain-openai<0.0.3,>=0.0.2->crewai) (0.5.2)
Requirement already satisfied: anyio<5,>=3.5.0 in c:\users\<USER>\appdata\local\programs\python\python39\lib\site-packages (from openai<2.0.0,>=1.7.1->crewai) (4.8.0)
Requirement already satisfied: distro<2,>=1.7.0 in c:\users\<USER>\appdata\local\programs\python\python39\lib\site-packages (from openai<2.0.0,>=1.7.1->crewai) (1.9.0)
Requirement already satisfied: httpx<1,>=0.23.0 in c:\users\<USER>\appdata\local\programs\python\python39\lib\site-packages (from openai<2.0.0,>=1.7.1->crewai) (0.28.1)
Requirement already satisfied: jiter<1,>=0.4.0 in c:\users\<USER>\appdata\local\programs\python\python39\lib\site-packages (from openai<2.0.0,>=1.7.1->crewai) (0.9.0)
Requirement already satisfied: sniffio in c:\users\<USER>\appdata\local\programs\python\python39\lib\site-packages (from openai<2.0.0,>=1.7.1->crewai) (1.3.1)
Requirement already satisfied: tqdm>4 in c:\users\<USER>\appdata\local\programs\python\python39\lib\site-packages (from openai<2.0.0,>=1.7.1->crewai) (4.67.1)
Requirement already satisfied: typing-extensions<5,>=4.11 in c:\users\<USER>\appdata\local\programs\python\python39\lib\site-packages (from openai<2.0.0,>=1.7.1->crewai) (4.12.2)
Requirement already satisfied: annotated-types>=0.6.0 in c:\users\<USER>\appdata\local\programs\python\python39\lib\site-packages (from pydantic<3.0.0,>=2.4.2->crewai) (0.7.0)
Requirement already satisfied: pydantic-core==2.27.2 in c:\users\<USER>\appdata\local\programs\python\python39\lib\site-packages (from pydantic<3.0.0,>=2.4.2->crewai) (2.27.2)
Requirement already satisfied: aiohappyeyeballs>=2.3.0 in c:\users\<USER>\appdata\local\programs\python\python39\lib\site-packages (from aiohttp<4.0.0,>=3.8.3->langchain<0.2.0,>=0.1.0->crewai) (2.4.6)
Requirement already satisfied: aiosignal>=1.1.2 in c:\users\<USER>\appdata\local\programs\python\python39\lib\site-packages (from aiohttp<4.0.0,>=3.8.3->langchain<0.2.0,>=0.1.0->crewai) (1.3.2)
Requirement already satisfied: attrs>=17.3.0 in c:\users\<USER>\appdata\local\programs\python\python39\lib\site-packages (from aiohttp<4.0.0,>=3.8.3->langchain<0.2.0,>=0.1.0->crewai) (25.1.0)
Requirement already satisfied: frozenlist>=1.1.1 in c:\users\<USER>\appdata\local\programs\python\python39\lib\site-packages (from aiohttp<4.0.0,>=3.8.3->langchain<0.2.0,>=0.1.0->crewai) (1.5.0)
Requirement already satisfied: multidict<7.0,>=4.5 in c:\users\<USER>\appdata\local\programs\python\python39\lib\site-packages (from aiohttp<4.0.0,>=3.8.3->langchain<0.2.0,>=0.1.0->crewai) (6.1.0)
Requirement already satisfied: propcache>=0.2.0 in c:\users\<USER>\appdata\local\programs\python\python39\lib\site-packages (from aiohttp<4.0.0,>=3.8.3->langchain<0.2.0,>=0.1.0->crewai) (0.3.0)
Requirement already satisfied: yarl<2.0,>=1.17.0 in c:\users\<USER>\appdata\local\programs\python\python39\lib\site-packages (from aiohttp<4.0.0,>=3.8.3->langchain<0.2.0,>=0.1.0->crewai) (1.18.3)
Requirement already satisfied: exceptiongroup>=1.0.2 in c:\users\<USER>\appdata\local\programs\python\python39\lib\site-packages (from anyio<5,>=3.5.0->openai<2.0.0,>=1.7.1->crewai) (1.2.2)
Requirement already satisfied: idna>=2.8 in c:\users\<USER>\appdata\local\programs\python\python39\lib\site-packages (from anyio<5,>=3.5.0->openai<2.0.0,>=1.7.1->crewai) (3.10)
Requirement already satisfied: marshmallow<4.0.0,>=3.18.0 in c:\users\<USER>\appdata\local\programs\python\python39\lib\site-packages (from dataclasses-json<0.7,>=0.5.7->langchain<0.2.0,>=0.1.0->crewai) (3.26.1)
Requirement already satisfied: typing-inspect<1,>=0.4.0 in c:\users\<USER>\appdata\local\programs\python\python39\lib\site-packages (from dataclasses-json<0.7,>=0.5.7->langchain<0.2.0,>=0.1.0->crewai) (0.9.0)
Requirement already satisfied: certifi in c:\users\<USER>\appdata\local\programs\python\python39\lib\site-packages (from httpx<1,>=0.23.0->openai<2.0.0,>=1.7.1->crewai) (2025.1.31)
Requirement already satisfied: httpcore==1.* in c:\users\<USER>\appdata\local\programs\python\python39\lib\site-packages (from httpx<1,>=0.23.0->openai<2.0.0,>=1.7.1->crewai) (1.0.7)
Requirement already satisfied: h11<0.15,>=0.13 in c:\users\<USER>\appdata\local\programs\python\python39\lib\site-packages (from httpcore==1.*->httpx<1,>=0.23.0->openai<2.0.0,>=1.7.1->crewai) (0.14.0)
Requirement already satisfied: jsonpatch<2.0,>=1.33 in c:\users\<USER>\appdata\local\programs\python\python39\lib\site-packages (from langchain-core<0.2.0,>=0.1.52->langchain<0.2.0,>=0.1.0->crewai) (1.33)
Requirement already satisfied: packaging<24.0,>=23.2 in c:\users\<USER>\appdata\local\programs\python\python39\lib\site-packages (from langchain-core<0.2.0,>=0.1.52->langchain<0.2.0,>=0.1.0->crewai) (23.2)
Requirement already satisfied: orjson<4.0.0,>=3.9.14 in c:\users\<USER>\appdata\local\programs\python\python39\lib\site-packages (from langsmith<0.2.0,>=0.1.17->langchain<0.2.0,>=0.1.0->crewai) (3.10.15)
Requirement already satisfied: requests-toolbelt<2.0.0,>=1.0.0 in c:\users\<USER>\appdata\local\programs\python\python39\lib\site-packages (from langsmith<0.2.0,>=0.1.17->langchain<0.2.0,>=0.1.0->crewai) (1.0.0)
Requirement already satisfied: charset-normalizer<4,>=2 in c:\users\<USER>\appdata\local\programs\python\python39\lib\site-packages (from requests<3,>=2->langchain<0.2.0,>=0.1.0->crewai) (3.4.1)
Requirement already satisfied: urllib3<3,>=1.21.1 in c:\users\<USER>\appdata\local\programs\python\python39\lib\site-packages (from requests<3,>=2->langchain<0.2.0,>=0.1.0->crewai) (2.3.0)
Requirement already satisfied: greenlet!=0.4.17 in c:\users\<USER>\appdata\local\programs\python\python39\lib\site-packages (from SQLAlchemy<3,>=1.4->langchain<0.2.0,>=0.1.0->crewai) (3.1.1)
Requirement already satisfied: regex>=2022.1.18 in c:\users\<USER>\appdata\local\programs\python\python39\lib\site-packages (from tiktoken<0.6.0,>=0.5.2->langchain-openai<0.0.3,>=0.0.2->crewai) (2024.11.6)
Requirement already satisfied: colorama in c:\users\<USER>\appdata\local\programs\python\python39\lib\site-packages (from tqdm>4->openai<2.0.0,>=1.7.1->crewai) (0.4.6)
Requirement already satisfied: jsonpointer>=1.9 in c:\users\<USER>\appdata\local\programs\python\python39\lib\site-packages (from jsonpatch<2.0,>=1.33->langchain-core<0.2.0,>=0.1.52->langchain<0.2.0,>=0.1.0->crewai) (3.0.0)
Requirement already satisfied: mypy-extensions>=0.3.0 in c:\users\<USER>\appdata\local\programs\python\python39\lib\site-packages (from typing-inspect<1,>=0.4.0->dataclasses-json<0.7,>=0.5.7->langchain<0.2.0,>=0.1.0->crewai) (1.0.0)
Using cached langchain-0.1.20-py3-none-any.whl (1.0 MB)
Using cached langchain_community-0.0.38-py3-none-any.whl (2.0 MB)
Using cached langchain_core-0.1.53-py3-none-any.whl (303 kB)
Using cached langchain_text_splitters-0.0.2-py3-none-any.whl (23 kB)
Using cached langsmith-0.1.147-py3-none-any.whl (311 kB)
Installing collected packages: langsmith, langchain-core, langchain-text-splitters, langchain-community, langchain
  Attempting uninstall: langsmith
    Found existing installation: langsmith 0.4.4
    Uninstalling langsmith-0.4.4:
      Successfully uninstalled langsmith-0.4.4
  Attempting uninstall: langchain-core
    Found existing installation: langchain-core 0.3.66
    Uninstalling langchain-core-0.3.66:
      Successfully uninstalled langchain-core-0.3.66
  Attempting uninstall: langchain-text-splitters
    Found existing installation: langchain-text-splitters 0.3.8
    Uninstalling langchain-text-splitters-0.3.8:
      Successfully uninstalled langchain-text-splitters-0.3.8
  Attempting uninstall: langchain-community
    Found existing installation: langchain-community 0.3.26
    Uninstalling langchain-community-0.3.26:
      Successfully uninstalled langchain-community-0.3.26
  Attempting uninstall: langchain
    Found existing installation: langchain 0.3.26
    Uninstalling langchain-0.3.26:
      Successfully uninstalled langchain-0.3.26
Successfully installed langchain-0.1.20 langchain-community-0.0.38 langchain-core-0.1.53 langchain-text-splitters-0.0.2 langsmith-0.1.147
